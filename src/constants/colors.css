/* Global CSS Custom Properties for AlphaGlobal Website */
/* This file should be imported in index.css to make colors available globally */

:root {
  /* Background Colors */
  --dark-blue: #040a0f;
  --medium-blue: #f7f9fc;
  --white: #ffffff;
  
  /* Brand Colors */
  --red: #840414;                   /* Legacy red - kept for compatibility */
  --header-footer-red: #8B1E2D;     /* RED for headers and footers - EXPERIMENT */
  --button-red: #8B1E2D;            /* RED for buttons and interactive elements - EXPERIMENT */
  --accent-1: #635bff;              /* Accent color for highlighting text */
  --button-text: #ffffff;           /* White text for buttons */
  --button-shadow: rgba(0, 0, 0, 0.6); /* strong neutral shadow for button hover */
  --card-color: #040a0f;            /* Color for cards and panels (dark blue) */
  --cta-background: transparent;    /* Transparent background for CTA sections */

  /* Frame Variables */
  --frame-color: #00ffc8;           /* Color for section frames */
  --frame-width: 30px;              /* Height/width of section frames */
  --frame2-color: #635bff;          /* Color for second frame type */
  --frame2-width: 30px;             /* Height/width of second frame type */
  
  /* Text Colors */
  --text-white: #ffffff;
  --text-black: #000000;
  --text-medium-blue: #000000;          /* Black text for cyan backgrounds */
  
  /* Utility Colors */
  --success-green: #3ed68c;
  --warning-yellow: #ffd93d;
  --error-red: #ff6b6b;
  --info-blue: #4BC0C0;
  
  /* Chart Colors */
  --chart-performance: #00ffc8;
  --chart-risk: #8B1E2D;
  --chart-fees: #635bff;
  --chart-liquidity: #ffd93d;

  /* Floating Bubbles Colors - EXPERIMENT */
  --bubble-blue: #587cff;
  --bubble-cyan: #00c4ff;
  --bubble-purple: #8032ff;
  --bubble-green: #00ffaa;
  --bubble-red: #8B1E2D;
  
  /* Transparency Variants */
  --white-10: rgba(255, 255, 255, 0.1);
  --white-20: rgba(255, 255, 255, 0.2);
  --white-30: rgba(255, 255, 255, 0.3);
  --white-80: rgba(255, 255, 255, 0.8);
  --black-10: rgba(0, 0, 0, 0.1);
  --black-20: rgba(0, 0, 0, 0.2);
  --black-30: rgba(0, 0, 0, 0.3);
  --black-50: rgba(0, 0, 0, 0.5);

  /* Spacing Variables */
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  --spacing-section: 120px;

  /* Border Radius Variables */
  --border-radius-xs: 4px;
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;

  /* Font Size Variables */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-md: 1rem;        /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.5rem;      /* 24px */
  --font-size-2xl: 2.0625rem;  /* 33px */
  --font-size-3xl: 3.5rem;     /* 56px */
  --font-size-4xl: 4rem;       /* 64px */
  --font-size-5xl: 5rem;       /* 80px */
  --font-size-6xl: 5.875rem;   /* 94px */

  /* Display Font Sizes */
  --font-size-display-1: 5rem;     /* 80px */
  --font-size-display-2: 4.5rem;   /* 72px */
  --font-size-display-3: 4rem;     /* 64px */
  --font-size-display-4: 2.8rem;   /* 45px */
  --font-size-display-5: 3rem;     /* 48px */
  --font-size-display-6: 2.5rem;   /* 40px */

  /* Shadow Variables */
  --shadow-xs: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-sm: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-md: 0 6px 20px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.25);

  /* Container Padding Variables */
  --container-padding-x: 20px;
  --container-padding-x-large: 48px;  /* Better padding for content sections */
  --container-padding-y: 60px;
  --section-padding-y: 120px;
  --section-padding-small: 30px;      /* For smaller sections like CTAs */
}

/* Global rules to ensure white text on blue backgrounds */
/* Any element with dark blue background must have white text */
[style*="background-color: #040a0f"],
[style*="backgroundColor: #040a0f"],
[style*="background-color: var(--dark-blue)"],
[style*="backgroundColor: var(--dark-blue)"],
.dark-blue-bg {
  color: white !important;
}

[style*="background-color: #040a0f"] *,
[style*="backgroundColor: #040a0f"] *,
[style*="background-color: var(--dark-blue)"] *,
[style*="backgroundColor: var(--dark-blue)"] *,
.dark-blue-bg * {
  color: white !important;
}

/* Any element with medium blue background must have black text */
[style*="background-color: #00ffc8"],
[style*="backgroundColor: #00ffc8"],
[style*="background-color: var(--medium-blue)"],
[style*="backgroundColor: var(--medium-blue)"],
.medium-blue-bg {
  color: var(--text-medium-blue) !important;
}

[style*="background-color: #00ffc8"] *,
[style*="backgroundColor: #00ffc8"] *,
[style*="background-color: var(--medium-blue)"] *,
[style*="backgroundColor: var(--medium-blue)"] *,
.medium-blue-bg * {
  color: var(--text-medium-blue) !important;
}

/* Utility classes for forcing white text */
.force-white-text,
.force-white-text * {
  color: white !important;
}

/* Full-width section styles for dark blue backgrounds */
.full-width-dark-section {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100vw !important;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw !important;
  margin-right: -50vw !important;
}

/* Full-width section styles for medium blue backgrounds */
.full-width-medium-section {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100vw !important;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw !important;
  margin-right: -50vw !important;
}



/* Full-width frame styles */
.full-width-frame {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100vw !important;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw !important;
  margin-right: -50vw !important;
}

/* Responsive adjustments for full-width sections */
@media (max-width: 768px) {
  .full-width-dark-section,
  .full-width-medium-section,
  .full-width-frame {
    width: 100vw !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    left: 50% !important;
    right: 50% !important;
  }

  .full-width-dark-section .container,
  .full-width-medium-section .container {
    padding-left: var(--container-padding-x) !important;
    padding-right: var(--container-padding-x) !important;
    max-width: none !important;
  }
}

/* Override any other color rules for blue backgrounds */
.dark-blue-bg h1, .dark-blue-bg h2, .dark-blue-bg h3, .dark-blue-bg h4, .dark-blue-bg h5, .dark-blue-bg h6 {
  color: white !important;
}

.dark-blue-bg p, .dark-blue-bg span, .dark-blue-bg div, .dark-blue-bg td, .dark-blue-bg th, .dark-blue-bg li {
  color: white !important;
}

/* Override any CSS that might set dark text colors */
.fund-content, .fund-content *,
.performance-container, .performance-container *,
.documents-container, .documents-container *,
.whatif-container, .whatif-container * {
  color: white !important;
}

/* Specific overrides for common dark text classes */
.summary-value, .summary-box h3, .summary-box p,
.document-title h3, .document-title p,
.risk-profile, .risk-profile *,
.fund-header, .fund-meta, .meta-item {
  color: white !important;
}
