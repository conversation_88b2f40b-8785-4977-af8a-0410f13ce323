/* Enhanced Tabs Component with animations and visual effects */
.tabs-container {
  width: 100%;
  margin-top: 0;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  transition: box-shadow 0.3s ease;
}

.tabs-container:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.tabs-header {
  margin-top: 0;
  position: relative;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 10px 0;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Active tab indicator - animated sliding bar */
.tabs-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: var(--indicator-left, 0);
  width: var(--indicator-width, 0);
  height: 3px;
  background-color: var(--primary);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  z-index: 2; /* Ensure it's above other elements */
  opacity: 1;
  border-radius: 3px 3px 0 0;
  transform: translateZ(0); /* Force hardware acceleration for smoother animations */
  will-change: left, width; /* Optimize for animations */
}

.tab-button {
  padding: 12px 16px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  position: relative;
  overflow: hidden;
  z-index: 1;
  white-space: nowrap;
  flex-shrink: 0;
}

/* Active tab styling */
.tab-button.active-tab {
  color: var(--primary, #333);
  border-bottom-color: var(--primary, #333);
  font-weight: 600;
}

/* Ensure all tabs are visible on all devices */
.tabs-header {
  display: flex !important;
  visibility: visible !important;
  justify-content: center;
}

/* For screens between 769px and 1024px (tablets and small laptops) */
@media (min-width: 769px) and (max-width: 1024px) {
  .tab-button {
    padding: 10px 14px; /* Further reduce padding for small laptops */
    font-size: 14px; /* Smaller font size */
  }
  
  .tabs-header {
    justify-content: flex-start; /* Start from left on smaller screens */
    padding: 0 10px; /* Add some horizontal padding */
  }
}

/* Hover effect with subtle background */
.tab-button:hover {
  color: var(--button-red);
  background-color: rgba(99, 91, 255, 0.05);
  transform: scale(1.02);
}

/* Ripple effect on click */
.tab-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(99, 91, 255, 0.3);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.tab-button:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

.tab-button.active-tab {
  color: var(--button-red);
  font-weight: 600;
  border-bottom: none; /* Remove the default border to avoid double underline */
  box-shadow: 0 4px 6px -6px rgba(99, 91, 255, 0.3);
  background-color: rgba(99, 91, 255, 0.05);
}

.tabs-body {
  padding: 15px 10px;
  min-height: 100px;
  position: relative;
}

.tab-content {
  animation: fadeIn 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);
  position: relative;
  padding-top: 20px; /* Add top padding */
}

/* Enhanced fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

/* Ripple animation for button clicks */
@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* Responsive styles for mobile */
@media (max-width: 768px) {
  .tabs-container {
    margin-top: 0;
  }
  
  .tabs-header {
    flex-wrap: nowrap; /* Prevent wrapping */
    justify-content: flex-start; /* Start from left */
    padding: 5px 0;
    margin-top: 0;
    margin-bottom: 10px;
    width: 100%;
    overflow-x: auto;
    display: flex !important;
    visibility: visible !important;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Hide scrollbar for Firefox */
    -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
  }
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  .tabs-header::-webkit-scrollbar {
    display: none;
  }
  
  .tab-button {
    padding: 8px 12px;
    font-size: 14px;
    margin: 0 2px;
    position: relative;
    min-width: auto;
    text-align: center;
    white-space: nowrap;
    flex-shrink: 0;
  }
}
