// Import Content Provider
import { ContentProvider } from './context/ContentContext';

import { useEffect } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import ReactGA from 'react-ga4';

import Layout from './App/Layout/Layout.jsx';
import Homepage from './Components/Homepage/Homepage.jsx';
import NotFound from './Components/NotFound/NotFound.jsx';

// Commented out other page imports - will be restored when needed
// import Funds from './Components/Funds/Funds.jsx';
// import FundsDetail from './Components/Funds/FundsDetail.jsx';
// import About from './Components/About/About.jsx';
// import Faq from './Components/FAQ/FAQ.jsx';
// import Contact from './Components/Contact/Contact.jsx';
// import AccountSignup from './Components/Signup/AccountSignup.jsx';
// import EmailSignup from './Components/Signup/EmailSignup.jsx';
// import GenAI from './Components/GenAI/GenAI.jsx';
// import GenAIDetail from './Components/GenAI/GenAIDetail.jsx';
// import Strategies from './Components/Strategies/Strategies.jsx';
// import StrategiesDetail from './Components/Strategies/StrategiesDetail.jsx';
// import Learn from './Components/Learn/Learn.jsx';
const TRACKING_ID = "G-Q44BREBPJ5";


function App() {
	 useEffect(() => {
        ReactGA.initialize(TRACKING_ID);
        // Send pageview with a custom path
        ReactGA.send({ hitType: "pageview", page: "/homepage", title: "Home Page" });
    }, [])

	return (
		<ContentProvider>
			<div className="">
				<BrowserRouter>
					<Routes>
						{/* Homepage Route */}
						<Route
							path="/"
							element={
								<Layout>
									<Homepage />
								</Layout>
							}
						></Route>

						{/* 404 Route - Must be last */}
						<Route
							path="*"
							element={
								<Layout>
									<NotFound />
								</Layout>
							}
						></Route>

						{/* Commented out other routes - will be restored when needed */}
						{/*
						<Route
							path="/funds"
							element={
								<Layout>
									<Funds />
								</Layout>
							}
						></Route>
						<Route
							path="/funds/:fundId"
							element={
								<Layout>
									<FundsDetail />
								</Layout>
							}
						></Route>
						<Route
							path="/genai"
							element={
								<Layout>
									<GenAI />
								</Layout>
							}
						></Route>
						<Route
							path="/genai/:genaiId"
							element={
								<Layout>
									<GenAIDetail />
								</Layout>
							}
						></Route>
						<Route
							path="/strategies"
							element={
								<Layout>
									<Strategies />
								</Layout>
							}
						></Route>
						<Route
							path="/strategies/:strategyId"
							element={
								<Layout>
									<StrategiesDetail />
								</Layout>
							}
						></Route>
						<Route
							path="/about"
							element={
								<Layout>
									<About />
								</Layout>
							}
						></Route>
						<Route
							path="/faq"
							element={
								<Layout>
									<Faq />
								</Layout>
							}
						></Route>
						<Route
							path="/contact"
							element={
								<Layout>
									<Contact />
								</Layout>
							}
						></Route>
						<Route
							path="/addemail"
							element={
								<Layout>
									<EmailSignup />
								</Layout>
							}
						></Route>
						<Route
							path="/signup"
							element={
								<Layout>
									<AccountSignup />
								</Layout>
							}
						></Route>
						<Route
							path="/learn"
							element={
								<Layout>
									<Learn />
								</Layout>
							}
						></Route>
						*/}
					</Routes>
				</BrowserRouter>
			</div>
		</ContentProvider>
	);
}
export default App;
