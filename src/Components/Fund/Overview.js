import React, { useEffect } from 'react';
import './fund.css';
import '../Common/StripeLikeBackground.css'; // Import CSS for fx-bubbles
import { COLORS } from '../../constants/colors';

const Overview = ({ fundDetails = {}, fundName }) => {



  useEffect(() => {
    return () => {
      console.log('Overview component unmounting');
    };
  }, [fundDetails, fundName]);

  return (
    <div style={{
      width: '100vw',
      marginLeft: 'calc(-50vw + 50%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Hero Section */}
      <section style={{
        backgroundColor: COLORS.MEDIUM_BLUE,
        paddingTop: '80px',
        paddingBottom: '60px',
        position: 'relative',
        width: '100%'
      }}>
        {/* Floating bubbles for visual consistency */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: '20px', paddingRight: '20px' }}>
          <div className="row justify-content-center">
            <div style={{ textAlign: 'center' }}>
              <h1 className="force-black-text" style={{ color: '#000000', fontWeight: 'bold', fontSize: '3rem', marginBottom: '1.5rem' }}>
                <span className="force-black-text" style={{ color: '#000000' }}>{fundName}</span>
              </h1>
              <p style={{
                color: '#000000',
                maxWidth: '800px',
                margin: '0 auto',
                fontSize: '1.2rem',
                marginBottom: '0'
              }}>
                {fundDetails?.description || fundDetails?.strategy || 'Professional cryptocurrency fund management with focus on long-term capital appreciation and risk management.'}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Fund Details Section */}
      <section style={{
        backgroundColor: COLORS.DARK_BLUE,
        paddingTop: '60px',
        paddingBottom: '60px',
        position: 'relative',
        width: '100%'
      }}>
        {/* Floating bubbles */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: '20px', paddingRight: '20px' }}>
          <div className="row justify-content-center mb-5">
            <div className="col-lg-10 text-center">
              <h2 className="h2 mb-4" style={{ color: 'var(--text-white)' }}>Fund Details</h2>
            </div>
          </div>

          <div className="row g-4 justify-content-center">
            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h6 className="card-subtitle mb-2" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Fund Manager
                  </h6>
                  <h5 className="card-title mb-0" style={{
                    color: 'var(--text-white)',
                    fontSize: '1.2rem',
                    fontWeight: '600'
                  }}>
                    {fundDetails?.manager || 'Moolah Capital'}
                  </h5>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h6 className="card-subtitle mb-2" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Inception Date
                  </h6>
                  <h5 className="card-title mb-0" style={{
                    color: 'var(--text-white)',
                    fontSize: '1.2rem',
                    fontWeight: '600'
                  }}>
                    {fundDetails?.start ? new Date(fundDetails.start).toLocaleDateString('en-US', { year: 'numeric', month: 'long' }) : 'January 2020'}
                  </h5>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h6 className="card-subtitle mb-2" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Minimum Investment
                  </h6>
                  <h5 className="card-title mb-0" style={{
                    color: 'var(--text-white)',
                    fontSize: '1.2rem',
                    fontWeight: '600'
                  }}>
                    {fundDetails?.minimumInvestment || '$10,000'}
                  </h5>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h6 className="card-subtitle mb-2" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Currency
                  </h6>
                  <h5 className="card-title mb-0" style={{
                    color: 'var(--text-white)',
                    fontSize: '1.2rem',
                    fontWeight: '600'
                  }}>
                    {fundDetails?.currency || 'USD'}
                  </h5>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Investment Approach Section */}
      <section style={{
        backgroundColor: COLORS.MEDIUM_BLUE,
        paddingTop: '60px',
        paddingBottom: '60px',
        position: 'relative',
        width: '100%'
      }}>
        {/* Floating bubbles */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: '20px', paddingRight: '20px' }}>
          <div className="row justify-content-center mb-5">
            <div className="col-lg-10 text-center">
              <h2 className="h2 mb-4" style={{ color: COLORS.TEXT_BLACK }}>Investment Approach</h2>
            </div>
          </div>

          <div className="row g-5">
            <div className="col-lg-6">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body">
                  <h4 className="card-title mb-3" style={{
                    color: COLORS.TEXT_BLACK,
                    borderLeft: `4px solid ${COLORS.RED}`,
                    paddingLeft: '15px'
                  }}>
                    Strategy
                  </h4>
                  <p className="card-text body-text" style={{ color: COLORS.TEXT_BLACK }}>
                    {fundDetails?.strategy || 'Advanced cryptocurrency portfolio management utilizing quantitative analysis and risk management techniques to optimize returns while managing downside risk.'}
                  </p>
                </div>
              </div>
            </div>

            <div className="col-lg-6">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body">
                  <h4 className="card-title mb-3" style={{
                    color: COLORS.TEXT_BLACK,
                    borderLeft: `4px solid ${COLORS.RED}`,
                    paddingLeft: '15px'
                  }}>
                    Investment Objective
                  </h4>
                  <p className="card-text body-text" style={{ color: COLORS.TEXT_BLACK }}>
                    {fundDetails?.investmentObjective || 'To achieve long-term capital appreciation by investing in a diversified portfolio of cryptocurrencies and blockchain-related assets while maintaining appropriate risk controls.'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Metrics Section */}
      <section style={{
        backgroundColor: COLORS.DARK_BLUE,
        paddingTop: '60px',
        paddingBottom: '60px',
        position: 'relative',
        width: '100%'
      }}>
        {/* Floating bubbles */}
        <div className="fx-bubbles" aria-hidden="true" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          pointerEvents: 'none'
        }}>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
          <span className="b"></span>
        </div>

        <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: '20px', paddingRight: '20px' }}>
          <div className="row justify-content-center mb-5">
            <div className="col-lg-10 text-center">
              <h2 className="h2 mb-4" style={{ color: 'var(--text-white)' }}>Key Metrics</h2>
            </div>
          </div>

          <div className="row g-4 justify-content-center">
            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h6 className="card-subtitle mb-2" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    1-Year Performance
                  </h6>
                  <h5 className="card-title mb-0" style={{
                    color: 'white',
                    fontSize: '1.2rem',
                    fontWeight: '600'
                  }}>
                    {fundDetails?.performance?.oneYear ? `${fundDetails.performance.oneYear}%` : 'N/A'}
                  </h5>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h6 className="card-subtitle mb-2" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Assets Under Management
                  </h6>
                  <h5 className="card-title mb-0" style={{
                    color: 'var(--text-white)',
                    fontSize: '1.2rem',
                    fontWeight: '600'
                  }}>
                    {typeof fundDetails?.aum === 'number' ? `$${fundDetails.aum}M` : fundDetails?.aum || 'N/A'}
                  </h5>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h6 className="card-subtitle mb-2" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Management Fee
                  </h6>
                  <h5 className="card-title mb-0" style={{
                    color: 'var(--text-white)',
                    fontSize: '1.2rem',
                    fontWeight: '600'
                  }}>
                    {fundDetails?.Fee || '2.0%'}
                  </h5>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100" style={{
                backgroundColor: 'var(--white-10)',
                border: 'none',
                borderRadius: 'var(--border-radius-md)',
                backdropFilter: 'blur(10px)'
              }}>
                <div className="card-body text-center">
                  <h6 className="card-subtitle mb-2" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                    Risk Level
                  </h6>
                  <h5 className="card-title mb-0" style={{
                    color: COLORS.TEXT_WHITE,
                    fontSize: '1.2rem',
                    fontWeight: '600'
                  }}>
                    {fundDetails?.riskLevel ? fundDetails.riskLevel.charAt(0).toUpperCase() + fundDetails.riskLevel.slice(1) : 'Medium'}
                  </h5>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Risk Meter Section */}
      <section style={{
        backgroundColor: COLORS.DARK_BLUE,
        paddingTop: '40px',
        paddingBottom: '60px',
        position: 'relative',
        width: '100%'
      }}>
        <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: '20px', paddingRight: '20px' }}>
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <div className="risk-meter" style={{
                backgroundColor: 'var(--white-10)',
                borderRadius: 'var(--border-radius-md)',
                padding: '30px',
                textAlign: 'center'
              }}>
                <h4 style={{ color: COLORS.TEXT_WHITE, marginBottom: '20px' }}>Risk Assessment</h4>

                {/* Risk level labels above */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontSize: '0.9rem',
                  color: COLORS.TEXT_WHITE,
                  marginBottom: '10px',
                  fontWeight: '500'
                }}>
                  <span>Low</span>
                  <span>Medium</span>
                  <span>High</span>
                </div>

                {/* Risk meter bar */}
                <div style={{
                  position: 'relative',
                  height: '20px',
                  backgroundColor: 'var(--white-20)',
                  borderRadius: '10px',
                  margin: '10px 0 20px 0'
                }}>
                  {/* Risk level indicator */}
                  <div style={{
                    position: 'absolute',
                    top: '50%',
                    left: fundDetails?.riskLevel?.toLowerCase() === 'low' ? '16.67%' :
                          fundDetails?.riskLevel?.toLowerCase() === 'medium' ? '50%' : '83.33%',
                    transform: 'translate(-50%, -50%)',
                    width: '16px',
                    height: '16px',
                    backgroundColor: fundDetails?.riskLevel?.toLowerCase() === 'high' ? COLORS.ERROR_RED :
                                   fundDetails?.riskLevel?.toLowerCase() === 'medium' ? COLORS.WARNING_YELLOW : COLORS.SUCCESS_GREEN,
                    borderRadius: '50%',
                    border: '2px solid white',
                    zIndex: 2
                  }}></div>
                </div>

                <p style={{
                  color: 'var(--white-80)',
                  fontSize: 'var(--font-size-sm)',
                  marginTop: '15px',
                  marginBottom: '0'
                }}>
                  Current Risk Level: <strong style={{ color: COLORS.TEXT_WHITE }}>
                    {fundDetails?.riskLevel ? fundDetails.riskLevel.charAt(0).toUpperCase() + fundDetails.riskLevel.slice(1) : 'Medium'}
                  </strong>
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Overview;