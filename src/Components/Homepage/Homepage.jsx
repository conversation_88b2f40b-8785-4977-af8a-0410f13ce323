import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import FundChart from './FundChart.jsx';
import PortfolioChart from './PortfolioChart.jsx';
import CTASection from '../../App/CTA/CTASection.jsx';

// Import global colors
import { COLORS } from '../../constants/colors';

// Use global colors directly

const Homepage = () => {
  const [videoLoaded, setVideoLoaded] = useState(false);
  const videoRef = useRef(null);

  const scrollToTop = useCallback(() => window.scrollTo(0, 0), []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) {
      console.error("Video ref is null");
      return;
    }

    //console.log("Video element exists:", video);

    const handleCanPlay = () => {
      console.log("Video can play now");
      // Set playback rate to exactly 0.30 (30% of normal rate)
      video.playbackRate = 0.30;
      console.log("Setting video playback rate to 0.30");

      video.play().catch((err) => {
        console.warn('Autoplay blocked:', err);
      });
      setVideoLoaded(true);
    };

    const handleError = (e) => {
      console.error('Video failed to load:', e);
      setVideoLoaded(true); // Still mark as loaded to remove loading state
    };

    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);

    // Try to manually trigger play and set playback rate
    setTimeout(() => {
      if (video && !videoLoaded) {
        console.log("Attempting manual play");
        // Ensure playback rate is set even if canplay event hasn't fired
        video.playbackRate = 0.30;
        console.log("Setting video playback rate to 0.30 (manual)");

        video.play().catch(err => console.warn("Manual play failed:", err));
      }
    }, 1000);

    return () => {
      if (video) {
        video.removeEventListener('canplay', handleCanPlay);
        video.removeEventListener('error', handleError);
      }
    };
  }, [videoLoaded]);

  // Add a separate effect to handle metadata loading
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleMetadataLoaded = () => {
      // Set playback rate when metadata is loaded
      video.playbackRate = 0.30;
      console.log("Metadata loaded - setting video playback rate to 0.30");
    };

    video.addEventListener('loadedmetadata', handleMetadataLoaded);

    return () => {
      video.removeEventListener('loadedmetadata', handleMetadataLoaded);
    };
  }, []);

  // Fund data for charts
  const fundData = [
    {
      name: "AlphaGlobal Market Index Fund",
      performance: 85,
      risk: 30,
      fees: 95,
      liquidity: 90
    },
    {
      name: "AlphaGlobal Momentum Fund",
      performance: 78,
      risk: 65,
      fees: 70,
      liquidity: 85
    },
    {
      name: "AlphaGlobal DeFi Leaders Fund",
      performance: 82,
      risk: 45,
      fees: 80,
      liquidity: 88
    },
    {
      name: "AlphaGlobal Yield Fund",
      performance: 87,
      risk: 47,
      fees: 75,
      liquidity: 83
    },
    {
      name: "AlphaGlobal GenAI Fund",
      performance: 85,
      risk: 48,
      fees: 76,
      liquidity: 85
    },
    {
      name: "Moolah Capital Portfolio",
      performance: 90,
      risk: 48,
      fees: 74,
      liquidity: 82
    }
  ];



  const panels = useMemo(() => [
    {
      title: 'Passive Index',
      desc: `<p>The passive fund follow a diversified mix of crypto assets or a broad market index to mirror overall market performance.</p>
             <p>It uses clear, rules-based methods, like market capitalization andtrading activity, to give low-cost exposure to the growth of the crypto market while spreading risk.</p>
             <p>Our research team reviews and adjusts the portfolio each month to keep it aligned with market trends and investment goals.</p>`,
      image: null,
      backgroundColor: 'var(--card-color)',      // Use card color
      link: '/funds#passive',
      infoLink: '/learn?topic=passive'
    },
    {
      title: 'Managed Funds',
      desc: `<p>Smart beta funds use algorithmic strategies to select and weight crypto assets based on factors like momentum or volatility, aiming to outperform the market.</p>
             <p>Special Situations funds focus on unique events that can create short-term opportunities.</p>
             <p>By diversifying across strategies and managers, these funds seek to capture targeted gains while managing risk.</p>`,
      image: null,
      backgroundColor: 'var(--card-color)',      // Use card color
      link: '/funds#smartbeta',
      infoLink: '/learn?topic=smartbeta'
    },
    {
      title: 'GenAI',
      desc: `<p>GenAI funds are built by algorithms powered by generative AI and large language models (LLMs) such as GPT</p>
             <p>Unlike passive index or actively managed funds, they are not ready-made. Each fund is created based on an investor’s chosen goals, risk level, and asset preferences.</p>
             <p>The AI processes market data, news, and blockchain activity to design the strategy and adjusts it as conditions change.</p>`,
      image: null,
      backgroundColor: 'var(--card-color)',      // Use card color
      link: '/funds#special',
      infoLink: '/learn?topic=special'
    }
  ], []);

  const sections = useMemo(() => [
    {
      id: "moolah-capital",
      key: "moolah-capital",
      title: `<span style="color: var(--accent-1)">Generative</span> AI and <span style="color: var(--accent-1)">ETFs</span> to boost your <span style="color: var(--accent-1)">Crypto</span> investments`,
      description: [
          "Join Moolah to grow your investment, diversify, hedge against risks, protect from frauds and cyber-hacks, and get a simple access to your money."
      ],
      image: null,
      chartData: fundData[0], // AlphaGlobal Market Index Fund
      link: "/funds",
      linkText: "Explore",
      secondaryLink: "/signup",
      secondaryLinkText: "Get Started",
      reverse: false,
      backgroundColor: COLORS.MEDIUM_BLUE, // Medium blue background
      textColor: COLORS.TEXT_BLACK, // Dark text for light background
      bubbles: true // Enable floating bubbles
    },
    {
      id: 'traditional-funds',
      key: 'traditional-funds',
      title: 'Moolah Funds',
      isCustomSection: true, // Flag to render custom content
      backgroundColor: COLORS.DARK_BLUE,
      textColor: COLORS.TEXT_WHITE, // White text for dark background
      bubbles: true // Enable floating bubbles
    },
    {
      id: 'moolah-engine',
      key: 'moolah-engine',
      title: 'Premium Funds',
      description: [
       "Moolah’s Signature Funds apply established investment styles to crypto in a format investors can access directly. Steady Guard, Smart Growth, and Trend Explorer are professionally managed portfolios that translate market-neutral, yield, value, and momentum approaches into diversified fund offerings. Each is built to reflect a clear style of investing, giving users straightforward entry into strategies that are normally complex to execute on their own."
      ],
      image: '/home-easy-safe.jpg',
      chartData: fundData[0], // AlphaGlobal Market Index Fund
      backgroundColor: COLORS.MEDIUM_BLUE, // Medium blue background
      textColor: COLORS.TEXT_BLACK, // Dark text for light background
      bubbles: true, // Enable floating bubbles
      link: "/funds/capital-fund",
      linkText: "Explore",
      reverse: true
    },
        {
      id: 'moolah-engine',
      key: 'moolah-engine',
      title: 'GenAI Funds',
      description: [
       "GenAI is purpose-built for index investing, trained on domain-specific data to generate investment ideas, asset mixes, and rebalancing strategies tailored to each investor’s profile. The result is a personalized index fund, your own ETF, blending AI-driven insights with professional oversight."
      ],
      image: '/home-easy-safe.jpg',
      chartData: fundData[0], // AlphaGlobal Market Index Fund
      backgroundColor: COLORS.DARK_BLUE, // Dark blue background
      textColor: COLORS.TEXT_WHITE, // White text for dark background
      bubbles: true, // Enable floating bubbles
      link: "/funds/capital-fund",
      linkText: "Explore",
      reverse: true
    },
    {
      id: 'moolah-sources',
      key: 'moolah-sources',
      title: 'Why Choosing Moolah',
      description: [
        "<h3 class='h3 mb-2'>A Smarter Approach to Digital Finance</h3><p class='body-text'>Moolah Capital delivers institutional-grade investment strategies tailored to the digital asset space—bridging traditional discipline with crypto opportunity.</p>",
        "<h3 class='h3 mb-2'>Built Around Your Goals</h3><p class='body-text'>We tailor portfolios to meet your investment objectives, actively managing risk, responding to market conditions, and ensuring full compliance.</p>",
        "<h3 class='h3 mb-2'>More Time, Less Complexity</h3><p class='body-text'>We handle the operational complexity—performance, compliance, and rebalancing—so you can focus on your own personal goals.</p>",
        "<h3 class='h3 mb-2'>Powered by Expert Research</h3><p class='body-text'>Our in-house research team fuels every investment decision, identifying high-conviction opportunities with disciplined risk management.</p>",
      ],
      image: '/home-easy-safe.jpg',
      useVideo: true,
      videoSrc: '/strategies-background.mp4',
      backgroundColor: COLORS.DARK_BLUE, // Dark blue background
      textColor: COLORS.TEXT_WHITE, // White text for dark background
      bubbles: true, // Enable floating bubbles
      link: "/funds/capital-fund",
      linkText: "Explore",
      reverse: false
    },
    {
      id: 'moolah-fund-options',
      key: 'moolah-fund-options',
      title: 'Moolah Fund Options',
      isCustomSection: true,
      backgroundColor: COLORS.MEDIUM_BLUE,
      textColor: COLORS.TEXT_BLACK,
      bubbles: true
    },
    {
      id: 'why-choose-moolah',
      key: 'why-choose-moolah',
      title: 'Why Choose Moolah Capital?',
      isCustomSection: true,
      backgroundColor: COLORS.DARK_BLUE,
      textColor: COLORS.TEXT_WHITE,
      bubbles: true
    }

  ], []);

  const reasons = useMemo(() => [
    {
      id: 1,
      title: "A Smarter Approach to Digital Finance",
      description: "Moolah Capital delivers institutional-grade investment strategies tailored to the digital asset space—bridging traditional discipline with crypto opportunity."
    },
    {
      id: 2,
      title: "Built Around Your Goals",
      description: "We tailor portfolios to meet your investment objectives, actively managing risk, responding to market conditions, and ensuring full compliance."
    },
    {
      id: 3,
      title: "More Time, Less Complexity",
      description: "We handle the operational complexity—performance, compliance, and rebalancing—so you can focus on your own personal goals."
    },
    {
      id: 4,
      title: "Powered by Expert Research",
      description: "Our in-house research team fuels every investment decision, identifying high-conviction opportunities with disciplined risk management."
    },
    {
      id: 5,
      title: "Lower Costs, Lower Risk",
      description: "Avoid the overhead of building your own investment function. You retain full ownership, while we deliver performance, compliance, and peace of mind."
    },
    {
      id: 6,
      title: "Fast, Responsive and Transparent",
      description: "We respond quickly to market shifts and keep you fully informed—clear communication, consistent reporting, no surprises."
    }
  ], []);

  return (
    <>
      {/* CSS to eliminate white margins */}
      <style>
        {`
          body, html {
            margin: 0 !important;
            padding: 0 !important;
          }

          footer, .footer, #footer {
            margin-top: 0 !important;
            padding-top: 0 !important;
          }

          .homepage_main {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          #home-cta {
            margin-bottom: 0 !important;
            padding-bottom: 30px !important;
          }

          .full-width-medium-section {
            margin-bottom: 0 !important;
          }

          /* Target any potential wrapper divs */
          .homepage_main > div:last-child {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          /* Ensure no gaps between sections and footer */
          section:last-of-type {
            margin-bottom: 0 !important;
          }
        `}
      </style>

      <div className="homepage_main position-relative" style={{
        marginTop: '0',
        marginBottom: '0',
        paddingTop: '0',
        paddingBottom: '0',
        backgroundColor: 'transparent'
      }}>


      {/* Content Container - Starts after full viewport */}
      <div className="page-content-container" style={{
        marginTop: '-100px',
        position: 'relative',
        zIndex: 2
      }}>
        <section id='home-sections' style={{ position: 'relative' }}>

      {sections.map((section, index) => {
        // Handle custom "Moolah Fund Options" section
        if (section.isCustomSection && section.id === 'moolah-fund-options') {
          return (
            <section key={section.id} style={{
              backgroundColor: COLORS.MEDIUM_BLUE,
              paddingTop: '60px',
              paddingBottom: '60px',
              position: 'relative',
              width: '100%'
            }}>
              {/* Floating bubbles */}
              <div className="fx-bubbles" aria-hidden="true" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 0,
                pointerEvents: 'none'
              }}>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
              </div>

              <div className="container" style={{ position: 'relative', zIndex: 1, paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)' }}>
                <div className="row justify-content-center mb-5">
                  <div className="col-lg-10 text-center">
                    <h2 className="h2 mb-4" style={{ color: COLORS.TEXT_BLACK }}>Moolah Fund Options</h2>
                  </div>
                </div>

                <div className="row g-5">
                  <div className="col-lg-4">
                    <div className="card h-100" style={{
                      backgroundColor: 'var(--white-10)',
                      border: 'none',
                      borderRadius: 'var(--border-radius-md)',
                      backdropFilter: 'blur(10px)'
                    }}>
                      <div className="card-body">
                        <h4 className="card-title mb-3" style={{
                          color: COLORS.TEXT_BLACK,
                          borderLeft: '4px solid var(--frame-color)',
                          paddingLeft: '15px'
                        }}>
                          Passive Funds
                        </h4>
                        <p className="card-text body-text" style={{ color: COLORS.TEXT_BLACK }}>
                          The passive fund follow a diversified mix of crypto assets or a broad market index to mirror overall market performance.
                          <br /><br />
                          It uses clear, rules-based methods, like market capitalization and trading activity, to give low-cost exposure to the growth of the crypto market while spreading risk.
                          <br /><br />
                          Our research team reviews and adjusts the portfolio each month to keep it aligned with market trends and investment goals.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="col-lg-4">
                    <div className="card h-100" style={{
                      backgroundColor: 'var(--white-10)',
                      border: 'none',
                      borderRadius: 'var(--border-radius-md)',
                      backdropFilter: 'blur(10px)'
                    }}>
                      <div className="card-body">
                        <h4 className="card-title mb-3" style={{
                          color: COLORS.TEXT_BLACK,
                          borderLeft: '4px solid var(--frame-color)',
                          paddingLeft: '15px'
                        }}>
                          Premium Funds
                        </h4>
                        <p className="card-text body-text" style={{ color: COLORS.TEXT_BLACK }}>
                          Premium funds apply established investment styles to crypto in a format investors can access directly.
                          <br /><br />
                          Steady Guard, Smart Growth, and Trend Explorer are professionally managed portfolios that translate market-neutral, yield, value, and momentum approaches into diversified fund offerings.
                          <br /><br />
                          Each is built to reflect a clear style of investing, giving users straightforward entry into strategies that are normally complex to execute on their own.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="col-lg-4">
                    <div className="card h-100" style={{
                      backgroundColor: 'var(--white-10)',
                      border: 'none',
                      borderRadius: 'var(--border-radius-md)',
                      backdropFilter: 'blur(10px)'
                    }}>
                      <div className="card-body">
                        <h4 className="card-title mb-3" style={{
                          color: COLORS.TEXT_BLACK,
                          borderLeft: '4px solid var(--frame-color)',
                          paddingLeft: '15px'
                        }}>
                          Gen AI
                        </h4>
                        <p className="card-text body-text" style={{ color: COLORS.TEXT_BLACK }}>
                          GenAI funds are built by algorithms powered by generative AI and large language models (LLMs) such as GPT.
                          <br /><br />
                          Unlike passive index or actively managed funds, they are not ready-made. Each fund is created based on an investor's chosen goals, risk level, and asset preferences.
                          <br /><br />
                          The AI processes market data, news, and blockchain activity to design the strategy and adjusts it as conditions change.
                        </p>
                      </div>
                    </div>
                  </div>

                </div>

                {/* CTA Section - Full Width */}
                <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
                  <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
                    <CTASection
                      title="Discover Moolah Funds"
                      cta="Explore"
                      link="/funds"
                      theme='learn'
                      style={{ width: '45%', minWidth: '250px' }}
                      className="cta-blue-section"
                      titleStyle={{ color: 'var(--text-medium-blue)' }}
                    />
                    <CTASection
                      title="Discover Moolah GenAI"
                      cta="Explore"
                      link="/genai"
                      theme='learn'
                      style={{ width: '45%', minWidth: '250px' }}
                      className="cta-blue-section"
                      titleStyle={{ color: 'var(--text-medium-blue)' }}
                    />
                  </div>
                </div>
              </div>
            </section>
          );
        }

        // Handle custom "Why Choose Moolah Capital?" section
        if (section.isCustomSection && section.id === 'why-choose-moolah') {
          return (
            <section
              key={section.id}
              className="full-width-dark-section"
              id={section.id}
              style={{
                backgroundColor: COLORS.DARK_BLUE,
                paddingTop: '120px',
                paddingBottom: '120px',
                paddingLeft: '0',
                paddingRight: '0',
                position: 'relative'
              }}
            >
              {/* Floating bubbles */}
              <div className="fx-bubbles" aria-hidden="true" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 0,
                pointerEvents: 'none'
              }}>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
              </div>

              <div className="container text-white" style={{ paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
                <div className="row justify-content-center mb-5">
                  <div className="col-lg-10 text-center">
                    <h2 className="h2 mb-4">Why Choose Moolah Capital?</h2>
                  </div>
                </div>

                <div className="row g-4 justify-content-center">
                  {/* First row - 2 cards */}
                  <div className="col-md-6 col-lg-4">
                    <div className="card h-100" style={{
                      backgroundColor: 'var(--white-10)',
                      border: 'none',
                      borderRadius: 'var(--border-radius-md)',
                      backdropFilter: 'blur(10px)'
                    }}>
                      <div className="card-body text-center">
                        <h5 className="card-title mb-3" style={{
                          color: 'var(--text-white)',
                          fontSize: '1.2rem',
                          fontWeight: '600'
                        }}>
                          A Smarter Approach to Digital Finance
                        </h5>
                        <p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                          Moolah Capital delivers institutional-grade investment strategies tailored to the digital asset space—bridging traditional discipline with crypto opportunity.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="col-md-6 col-lg-4">
                    <div className="card h-100" style={{
                      backgroundColor: 'var(--white-10)',
                      border: 'none',
                      borderRadius: 'var(--border-radius-md)',
                      backdropFilter: 'blur(10px)'
                    }}>
                      <div className="card-body text-center">
                        <h5 className="card-title mb-3" style={{
                          color: 'var(--text-white)',
                          fontSize: '1.2rem',
                          fontWeight: '600'
                        }}>
                          Built Around Your Goals
                        </h5>
                        <p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                          We tailor portfolios to meet your investment objectives, actively managing risk, responding to market conditions, and ensuring full compliance.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="col-md-6 col-lg-4">
                    <div className="card h-100" style={{
                      backgroundColor: 'var(--white-10)',
                      border: 'none',
                      borderRadius: 'var(--border-radius-md)',
                      backdropFilter: 'blur(10px)'
                    }}>
                      <div className="card-body text-center">
                        <h5 className="card-title mb-3" style={{
                          color: 'var(--text-white)',
                          fontSize: '1.2rem',
                          fontWeight: '600'
                        }}>
                          More Time, Less Complexity
                        </h5>
                        <p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                          We handle the operational complexity—performance, compliance, and rebalancing—so you can focus on your own personal goals.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="col-md-6 col-lg-4">
                    <div className="card h-100" style={{
                      backgroundColor: 'var(--white-10)',
                      border: 'none',
                      borderRadius: 'var(--border-radius-md)',
                      backdropFilter: 'blur(10px)'
                    }}>
                      <div className="card-body text-center">
                        <h5 className="card-title mb-3" style={{
                          color: 'var(--text-white)',
                          fontSize: '1.2rem',
                          fontWeight: '600'
                        }}>
                          Powered by Expert Research
                        </h5>
                        <p className="card-text" style={{ color: 'var(--white-80)', fontSize: 'var(--font-size-sm)' }}>
                          Our in-house research team fuels every investment decision, identifying high-conviction opportunities with disciplined risk management.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          );
        }

        // Handle custom "Level I AI-powered Funds" section
        if (section.isCustomSection && section.id === 'traditional-funds') {
          return (
            <section
              key={section.id}
              className="full-width-dark-section"
              id={section.id}
              style={{
                backgroundColor: COLORS.DARK_BLUE,
                paddingTop: '120px',
                paddingBottom: '120px',
                paddingLeft: '0',
                paddingRight: '0',
                position: 'relative'
              }}
            >
              {/* Floating bubbles */}
              <div className="fx-bubbles" aria-hidden="true" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 0,
                pointerEvents: 'none'
              }}>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
                <span className="b"></span>
              </div>

              <div className="container text-white" style={{ paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)', position: 'relative', zIndex: 1 }}>
                <div className="row justify-content-center mb-5">
                  <div className="col-lg-10 text-center">
                    <h2 className="h2 mb-4">Moolah Funds</h2>
                  </div>
                </div>

                <div className="row g-4 justify-content-center">
                  {/* First row - 2 cards */}
                  <div className="col-md-6 col-lg-6">
                    <Link to="/funds/capital-fund" className="text-decoration-none">
                      <div className="card h-100" style={{
                        backgroundColor: 'var(--white-10)',
                        border: 'none',
                        borderRadius: 'var(--border-radius-md)',
                        backdropFilter: 'blur(10px)',
                        cursor: 'pointer',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'scale(1.02)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }}>
                      <div className="card-body text-center">
                        <h5 className="card-title mb-3" style={{
                          color: 'var(--text-white)',
                          fontSize: '1.4rem',
                          fontWeight: '600'
                        }}>
                          STEADY GUARD
                        </h5>
                        <p className="card-text" style={{ color: 'var(--white-80)', fontSize: '1.2rem' }}>
                          A passive, market-neutral strategy that emphasizes diversification and stability through defensive positioning.
                        </p>
                      </div>
                      </div>
                    </Link>
                  </div>

                  <div className="col-md-6 col-lg-6">
                    <Link to="/funds/momentum-fund" className="text-decoration-none">
                      <div className="card h-100" style={{
                        backgroundColor: 'var(--white-10)',
                        border: 'none',
                        borderRadius: 'var(--border-radius-md)',
                        backdropFilter: 'blur(10px)',
                        cursor: 'pointer',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'scale(1.02)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }}>
                        <div className="card-body text-center">
                          <h5 className="card-title mb-3" style={{
                            color: 'var(--text-white)',
                            fontSize: '1.4rem',
                            fontWeight: '600'
                          }}>
                            SMART GROWTH
                          </h5>
                          <p className="card-text" style={{ color: 'var(--white-80)', fontSize: '1.2rem' }}>
                            A diversified smart-beta portfolio combining passive yield strategies with value-driven crypto projects for sustainable long-term growth.
                          </p>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>

                <div className="row g-4 justify-content-center mt-4">
                  {/* Second row - 2 cards */}
                  <div className="col-md-6 col-lg-6">
                    <Link to="/funds/income-fund" className="text-decoration-none">
                      <div className="card h-100" style={{
                        backgroundColor: 'var(--white-10)',
                        border: 'none',
                        borderRadius: 'var(--border-radius-md)',
                        backdropFilter: 'blur(10px)',
                        cursor: 'pointer',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'scale(1.02)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }}>
                        <div className="card-body text-center">
                          <h5 className="card-title mb-3" style={{
                            color: 'var(--text-white)',
                            fontSize: '1.4rem',
                            fontWeight: '600'
                          }}>
                            TREND EXPLORER
                          </h5>
                          <p className="card-text" style={{ color: 'var(--white-80)', fontSize: '1.2rem' }}>
                            A smart-beta strategy with active oversight, built to capture crypto momentum and explore high-upside opportunities.
                          </p>
                        </div>
                      </div>
                    </Link>
                  </div>

                  <div className="col-md-6 col-lg-6">
                    <Link to="/funds/defi-fund" className="text-decoration-none">
                      <div className="card h-100" style={{
                        backgroundColor: 'var(--white-10)',
                        border: 'none',
                        borderRadius: 'var(--border-radius-md)',
                        backdropFilter: 'blur(10px)',
                        cursor: 'pointer',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'scale(1.02)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }}>
                        <div className="card-body text-center">
                          <h5 className="card-title mb-3" style={{
                            color: 'var(--text-white)',
                            fontSize: '1.4rem',
                            fontWeight: '600'
                          }}>
                            Gen AI
                          </h5>
                          <p className="card-text" style={{ color: 'var(--white-80)', fontSize: '1.2rem' }}>
                            An active, fully personalized ETF where diversification and allocation are dynamically tailored to each investor’s goals.
                          </p>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </section>
          );
        }

        // Regular section rendering
        return (
        <section
          key={section.id}
          className={`${(section.backgroundColor === COLORS.DARK_BLUE || section.backgroundColor === '#1A365D') ? 'full-width-dark-section' : (section.backgroundColor === COLORS.MEDIUM_BLUE) ? 'full-width-medium-section' : ''}`}
          id={section.id}
          style={{
            backgroundColor: section.backgroundColor || (index % 2 === 0 ? COLORS.MEDIUM_BLUE : COLORS.DARK_BLUE),
            minHeight: '500px',
            paddingTop: section.id === 'moolah-capital' ? '180px' : '60px',
            paddingBottom: '60px',
            paddingLeft: (section.backgroundColor === COLORS.DARK_BLUE || section.backgroundColor === COLORS.MEDIUM_BLUE || section.backgroundColor === '#1A365D') ? '0' : '20px',
            paddingRight: (section.backgroundColor === COLORS.DARK_BLUE || section.backgroundColor === COLORS.MEDIUM_BLUE || section.backgroundColor === '#1A365D') ? '0' : '20px',
            display: 'flex',
            alignItems: 'center',
            position: 'relative',
            marginTop: index === 0 ? '-1px' : '0',
            marginBottom: '0'
          }}
        >
          {section.bubbles && (
            <div className="fx-bubbles" aria-hidden="true">
              <span className="b"></span>
              <span className="b"></span>
              <span className="b"></span>
              <span className="b"></span>
              <span className="b"></span>
            </div>
          )}

          <div className={`container ${
            (index < 5) // First 5 sections have black text on grey background
              ? 'text-black'
              : (section.backgroundColor === '#1A365D' || section.backgroundColor === COLORS.DARK_BLUE || (index % 2 !== 0 && !section.backgroundColor))
                ? 'text-white'
                : 'text-black'
          }`} style={{
            fontWeight: 'normal',
            paddingLeft: '20px',
            paddingRight: '20px'
          }}>
            {typeof section.content === 'object' && React.isValidElement(section.content) ? (
              section.content
            ) : Array.isArray(section.content) ? (
              section.content
            ) : (
              <div className="row align-items-center">
                <div className={`col-lg-5 text-center text-center-mbl ${section.reverse ? 'order-lg-1' : 'order-lg-2'} order-1 mb-4 mb-lg-0`}>
                  {section.chartData && index === 0 ? (
                    // Custom performance chart for the first section
                    <Link to="/funds" onClick={scrollToTop} style={{ textDecoration: 'none' }}>
                      <div style={{
                        backgroundColor: 'transparent',
                        maxWidth: '1200px',
                        margin: '0 auto',
                        border: 'none',
                        borderRadius: '0.5rem',
                        boxShadow: 'none',
                        padding: '20px',
                        cursor: 'pointer',
                        transition: 'transform 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'scale(1.02)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }}>
                        <div style={{ position: 'relative', height: '600px', width: '100%' }}>
                          {/* Legend - top middle, one line, smaller font */}
                          <div style={{
                            position: 'absolute',
                            top: '120px',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            display: 'flex',
                            gap: '15px',
                            fontSize: '12px',
                            zIndex: 10
                          }}>
                            <span style={{ color: 'var(--text-medium-blue)' }}>
                              <span style={{ color: COLORS.RED }}>●</span> Moolah Funds
                            </span>
                            <span style={{ color: 'var(--text-medium-blue)' }}>
                              <span style={{ color: COLORS.FRAME2_COLOR }}>●</span> Market Index
                            </span>
                          </div>

                          <svg width="100%" height="100%" viewBox="0 0 1200 600" style={{ overflow: 'visible', marginTop: '20px' }}>
                            {/* Grid lines */}
                            <defs>
                              <pattern id="grid" width="120" height="80" patternUnits="userSpaceOnUse">
                                <path d="M 120 0 L 0 0 0 80" fill="none" stroke="#e0e0e0" strokeWidth="0.5" opacity="0.2"/>
                              </pattern>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#grid)" />

                            {/* Y-axis labels - 0%, 30%, 70% - more transparent */}
                            <text x="40" y="520" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">0%</text>
                            <text x="40" y="350" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">30%</text>
                            <text x="40" y="150" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">70%</text>

                            {/* X-axis labels - 2022, 2023, 2024 - more transparent */}
                            <text x="250" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2022</text>
                            <text x="600" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2023</text>
                            <text x="950" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2024</text>

                            {/* Performance line 1 - RED (very transparent) */}
                            <path
                              d="M 250 480 L 600 320 L 950 180"
                              fill="none"
                              stroke={COLORS.RED}
                              strokeWidth="6"
                              strokeLinecap="round"
                              opacity="0.3"
                            />

                            {/* Performance line 2 - FRAME2_COLOR (very transparent) */}
                            <path
                              d="M 250 500 L 600 380 L 950 250"
                              fill="none"
                              stroke={COLORS.FRAME2_COLOR}
                              strokeWidth="6"
                              strokeLinecap="round"
                              opacity="0.3"
                            />

                            {/* Data points for line 1 */}
                            <circle cx="250" cy="480" r="8" fill={COLORS.RED} opacity="0.6" />
                            <circle cx="600" cy="320" r="8" fill={COLORS.RED} opacity="0.6" />
                            <circle cx="950" cy="180" r="8" fill={COLORS.RED} opacity="0.6" />

                            {/* Data points for line 2 */}
                            <circle cx="250" cy="500" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                            <circle cx="600" cy="380" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                            <circle cx="950" cy="250" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                          </svg>
                        </div>
                      </div>
                    </Link>
                  ) : section.chartData && index === 2 ? (
                    // Performance chart for Level II AI-generated index funds (second section)
                    <Link to="/genai" onClick={scrollToTop} style={{ textDecoration: 'none' }}>
                      <div style={{
                        backgroundColor: 'transparent',
                        maxWidth: '1200px',
                        margin: '0 auto',
                        border: 'none',
                        borderRadius: '0.5rem',
                        boxShadow: 'none',
                        padding: '20px',
                        cursor: 'pointer',
                        transition: 'transform 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'scale(1.02)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }}>
                        <div style={{ position: 'relative', height: '600px', width: '100%' }}>
                          {/* Legend - top middle, one line, smaller font */}
                          <div style={{
                            position: 'absolute',
                            top: '120px',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            display: 'flex',
                            gap: '15px',
                            fontSize: '12px',
                            zIndex: 10
                          }}>
                            <span style={{ color: 'var(--text-medium-blue)' }}>
                              <span style={{ color: COLORS.RED }}>●</span> GenAI Funds
                            </span>
                            <span style={{ color: 'var(--text-medium-blue)' }}>
                              <span style={{ color: COLORS.FRAME2_COLOR }}>●</span> Market Index
                            </span>
                          </div>

                          <svg width="100%" height="100%" viewBox="0 0 1200 600" style={{ overflow: 'visible', marginTop: '20px' }}>
                            {/* Grid lines */}
                            <defs>
                              <pattern id="grid2" width="120" height="80" patternUnits="userSpaceOnUse">
                                <path d="M 120 0 L 0 0 0 80" fill="none" stroke="#e0e0e0" strokeWidth="0.5" opacity="0.2"/>
                              </pattern>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#grid2)" />

                            {/* Y-axis labels - 0%, 30%, 70% - more transparent */}
                            <text x="40" y="520" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">0%</text>
                            <text x="40" y="350" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">30%</text>
                            <text x="40" y="150" fill="var(--text-medium-blue)" fontSize="18" textAnchor="start" opacity="0.5">70%</text>

                            {/* X-axis labels - 2022, 2023, 2024 - more transparent */}
                            <text x="250" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2022</text>
                            <text x="600" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2023</text>
                            <text x="950" y="560" fill="var(--text-medium-blue)" fontSize="18" textAnchor="middle" opacity="0.5">2024</text>

                            {/* Performance line 1 - RED (very transparent) */}
                            <path
                              d="M 250 480 L 600 320 L 950 180"
                              fill="none"
                              stroke={COLORS.RED}
                              strokeWidth="6"
                              strokeLinecap="round"
                              opacity="0.3"
                            />

                            {/* Performance line 2 - FRAME2_COLOR (very transparent) */}
                            <path
                              d="M 250 500 L 600 380 L 950 250"
                              fill="none"
                              stroke={COLORS.FRAME2_COLOR}
                              strokeWidth="6"
                              strokeLinecap="round"
                              opacity="0.3"
                            />

                            {/* Data points for line 1 */}
                            <circle cx="250" cy="480" r="8" fill={COLORS.RED} opacity="0.6" />
                            <circle cx="600" cy="320" r="8" fill={COLORS.RED} opacity="0.6" />
                            <circle cx="950" cy="180" r="8" fill={COLORS.RED} opacity="0.6" />

                            {/* Data points for line 2 */}
                            <circle cx="250" cy="500" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                            <circle cx="600" cy="380" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                            <circle cx="950" cy="250" r="8" fill={COLORS.FRAME2_COLOR} opacity="0.6" />
                          </svg>
                        </div>
                      </div>
                    </Link>
                  ) : section.useVideo ? (
                    <video
                      src={section.videoSrc}
                      autoPlay
                      loop
                      muted
                      playsInline
                      style={{
                        maxWidth: '100%',
                        maxHeight: '600px',
                        objectFit: 'cover',
                        width: '100%',
                        borderRadius: '12px'
                      }}
                    />
                  ) : section.chartData ? (
                    <div style={{
                      backgroundColor: 'transparent',
                      maxWidth: '400px',
                      margin: '0 auto',
                      border: 'none',
                      borderRadius: '0.5rem',
                      boxShadow: 'none'
                    }}>
                      {section.chartData.type === 'portfolio' ? (
                        <PortfolioChart
                          portfolioData={section.chartData}
                          compact={false}
                          textColor={(index < 5) ? 'text-dark' : (section.backgroundColor === '#1A365D' || section.backgroundColor === COLORS.DARK_BLUE) ? 'text-white' : 'text-dark'}
                          backgroundColor="transparent"
                        />
                      ) : (
                        <FundChart
                          fundData={section.chartData}
                          compact={false}
                          textColor={(index < 5) ? 'text-dark' : (section.backgroundColor === '#1A365D' || section.backgroundColor === COLORS.DARK_BLUE) ? 'text-white' : 'text-dark'}
                          backgroundColor="transparent"
                        />
                      )}
                    </div>
                  ) : section.image ? (
                    <img
                      alt={section.title}
                      className="img-fluid ms-4 rounded-lg"
                      loading="lazy"
                      src={section.image}
                      style={{ maxWidth: '100%', maxHeight: '300px', objectFit: 'cover', width: 'auto' }}
                      onError={(e) => {
                        e.target.onerror = null; // Prevent infinite loop
                        e.target.src = "/placeholder.jpg";
                      }}
                    />
                  ) : null}
                </div>
                <div className={`col-lg-7 text-lg-start text-center ${section.reverse ? 'order-lg-2' : 'order-lg-1'} order-2`}>
                  <h2 className={`${section.id === 'moolah-capital' ? 'h1-hero' : 'h2'} ${section.id === 'moolah-sources' ? '' : 'ms-4'}`} style={{
                    fontSize: section.id === 'moolah-capital' ? 'var(--font-size-6xl)' :
                              section.id === 'moolah-engine' || section.id === 'moolah-sources' ? '2.8rem' : 'inherit',
                    fontWeight: section.id === 'moolah-capital' ? '700' :
                               section.id === 'moolah-engine' || section.id === 'moolah-sources' ? '700' : 'inherit',
                    lineHeight: section.id === 'moolah-capital' ? '1.0' :
                               section.id === 'moolah-engine' || section.id === 'moolah-sources' ? '1.2' : 'inherit',
                    marginBottom: section.id === 'moolah-capital' ? '2rem' :
                                 section.id === 'moolah-engine' || section.id === 'moolah-sources' ? '2rem' : 'inherit',
                    color: section.textColor || 'inherit'
                  }} dangerouslySetInnerHTML={{ __html: section.title }}></h2>
                  {typeof section.description === 'string' ? (
                    <p className={`body-text ${section.id === 'moolah-sources' ? '' : 'ms-4 ms-lg-4'}`} style={{ color: section.textColor || 'inherit' }} dangerouslySetInnerHTML={{ __html: section.description }}></p>
                  ) : Array.isArray(section.description) ? (
                    section.description.map((paragraph, idx) => (
                      <p key={idx} className={`body-text ${section.id === 'moolah-sources' ? '' : 'ms-lg-4'}`} style={{ color: section.textColor || 'inherit' }} dangerouslySetInnerHTML={{ __html: paragraph }}></p>
                    ))
                  ) : null}
                  {section.link && (
                    <div className="text-lg-start text-center">
                      <Link role="button" className={`btn sec-4-button ${section.id === 'moolah-sources' ? '' : 'ms-lg-4'} mt-4`} onClick={scrollToTop} to={section.link}>
                        {section.linkText || "Explore"}
                      </Link>
                      {section.secondaryLink && (
                        <Link role="button" className="btn sec-4-button ms-lg-2 mt-4" onClick={scrollToTop} to={section.secondaryLink}>
                          {section.secondaryLinkText || "Get Started"}
                        </Link>
                      )}
                    </div>
                  )}

                </div>
              </div>
            )}
          </div>
        </section>
        );
      })}
      </section>














      <section id="funds-cta1" className="full-width-dark-section" style={{ backgroundColor: COLORS.DARK_BLUE, paddingTop: '30px', paddingBottom: '30px', paddingLeft: '0', paddingRight: '0' }}>
        <div className="container text-center-mbl text-white" style={{ paddingLeft: 'var(--container-padding-x)', paddingRight: 'var(--container-padding-x)' }}>
          <CTASection
            title="Start Your Financial Journey"
            cta="Sign Up"
            link="/signup#account-signup"
            theme="signup"
            titleStyle={{ color: 'white' }}
          />
        </div>
      </section>
      </div>
    </div>
    </>
  );
};

export default Homepage;
